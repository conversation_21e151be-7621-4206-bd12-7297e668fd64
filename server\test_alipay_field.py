"""
测试脚本：验证用户模型的alipay字段功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.user import User, UserStatus
from models.base import SessionLocal
from database import init_db
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_user_model_with_alipay():
    """测试用户模型的alipay字段"""
    try:
        # 初始化数据库
        init_db()
        
        # 创建数据库会话
        db = SessionLocal()
        
        # 测试创建用户（带支付宝手机号）
        test_user_1 = User(
            username="test_user_alipay",
            password_hash="hashed_password_123",
            alipay="13800138000",
            status=UserStatus.active
        )
        
        db.add(test_user_1)
        db.commit()
        db.refresh(test_user_1)
        
        logger.info(f"创建用户成功: {test_user_1}")
        logger.info(f"用户支付宝手机号: {test_user_1.alipay}")
        
        # 测试创建用户（不带支付宝手机号）
        test_user_2 = User(
            username="test_user_no_alipay",
            password_hash="hashed_password_456",
            alipay=None,
            status=UserStatus.active
        )
        
        db.add(test_user_2)
        db.commit()
        db.refresh(test_user_2)
        
        logger.info(f"创建用户成功: {test_user_2}")
        logger.info(f"用户支付宝手机号: {test_user_2.alipay}")
        
        # 测试查询用户
        users = db.query(User).filter(User.username.like("test_user_%")).all()
        logger.info(f"查询到 {len(users)} 个测试用户")
        
        for user in users:
            logger.info(f"用户: {user.username}, 支付宝: {user.alipay}")
        
        # 清理测试数据
        db.query(User).filter(User.username.like("test_user_%")).delete()
        db.commit()
        
        logger.info("测试完成，清理测试数据")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        if db:
            db.rollback()
        raise
    finally:
        if db:
            db.close()


def test_alipay_uniqueness():
    """测试支付宝手机号唯一性"""
    try:
        db = SessionLocal()
        
        # 创建第一个用户
        user1 = User(
            username="user_alipay_1",
            password_hash="hash1",
            alipay="13900139000"
        )
        db.add(user1)
        db.commit()
        
        # 尝试创建相同支付宝手机号的用户
        user2 = User(
            username="user_alipay_2", 
            password_hash="hash2",
            alipay="13900139000"
        )
        db.add(user2)
        
        try:
            db.commit()
            logger.warning("支付宝手机号重复检查失败 - 应该抛出异常")
        except Exception as e:
            logger.info(f"支付宝手机号重复检查正常: {e}")
            db.rollback()
        
        # 清理测试数据
        db.query(User).filter(User.username.like("user_alipay_%")).delete()
        db.commit()
        
    except Exception as e:
        logger.error(f"唯一性测试失败: {e}")
        if db:
            db.rollback()
    finally:
        if db:
            db.close()


if __name__ == "__main__":
    logger.info("开始测试用户模型的alipay字段")
    
    # 测试基本功能
    test_user_model_with_alipay()
    
    # 测试唯一性（注意：数据库层面可能没有唯一约束，需要在应用层检查）
    test_alipay_uniqueness()
    
    logger.info("所有测试完成")
