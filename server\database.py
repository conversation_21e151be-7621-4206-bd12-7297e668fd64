"""
数据库初始化和管理
"""
from models.base import engine, SessionLocal, Base
from models import Platform, PlatformStatus, Admin, AdminRole  # 导入需要的模型以确保表被创建
from utils.auth import hash_password  # 导入密码加密函数
import logging

logger = logging.getLogger(__name__)


def init_db():
    """初始化数据库，创建所有表和初始数据"""
    try:
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        logger.info("数据库表创建成功")

        # 运行数据库迁移（如果需要）
        run_migrations()

        # 插入初始数据
        insert_initial_data()
        logger.info("初始数据插入成功")

    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


def run_migrations():
    """运行数据库迁移"""
    try:
        from migrations.email_to_alipay import migrate_email_to_alipay
        migrate_email_to_alipay()
        logger.info("数据库迁移完成")
    except ImportError:
        logger.info("没有找到迁移脚本，跳过迁移")
    except Exception as e:
        logger.warning(f"数据库迁移失败: {e}")
        # 迁移失败不应该阻止应用启动


def insert_initial_data():
    """插入初始数据"""
    db = SessionLocal()
    try:
        # 检查是否已有数据，避免重复插入
        existing_platforms = db.query(Platform).count()
        if existing_platforms > 0:
            logger.info("初始数据已存在，跳过插入")
            return
        
        # 插入平台数据
        platforms_data = [
            {
                'code': 'tb',
                'name': '淘宝',
                'base_price': 1.00,
                'commission_rate': 0.10,
                'status': PlatformStatus.active
            },
            {
                'code': 'jd', 
                'name': '京东',
                'base_price': 0.80,
                'commission_rate': 0.08,
                'status': PlatformStatus.active
            },
            {
                'code': 'pdd',
                'name': '拼多多', 
                'base_price': 0.60,
                'commission_rate': 0.06,
                'status': PlatformStatus.active
            }
        ]
        
        for platform_data in platforms_data:
            platform = Platform(**platform_data)
            db.add(platform)
        
      
        
        # 创建默认管理员账号，使用bcrypt加密密码
        # 决策理由：使用与用户注册相同的bcrypt加密方式确保密码安全性一致
        admin = Admin(
            username='admin',
            password_hash=hash_password('password'),
            role=AdminRole.super_admin
        )
        db.add(admin)
        
        db.commit()
        logger.info("初始数据插入完成")
        
    except Exception as e:
        db.rollback()
        logger.error(f"初始数据插入失败: {e}")
        raise
    finally:
        db.close()


def reset_db():
    """重置数据库（删除所有表并重新创建）"""
    try:
        # 删除所有表
        Base.metadata.drop_all(bind=engine)
        logger.info("数据库表删除成功")
        
        # 重新初始化
        init_db()
        logger.info("数据库重置完成")
        
    except Exception as e:
        logger.error(f"数据库重置失败: {e}")
        raise


def get_db_session():
    """获取数据库会话（用于依赖注入）"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


if __name__ == "__main__":
    # 直接运行此文件时初始化数据库
    logging.basicConfig(level=logging.INFO)
    init_db()
    print("数据库初始化完成！")
