"""
数据库迁移脚本：将用户表的email字段替换为alipay字段
文件名: email_to_alipay.py
"""
import sqlite3
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


def migrate_email_to_alipay(db_path: str = "ecommerce_crawler.db"):
    """
    将用户表的email字段迁移为alipay字段
    
    Args:
        db_path: 数据库文件路径
    """
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否已经存在alipay字段
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'alipay' in columns:
            logger.info("alipay字段已存在，跳过迁移")
            return
        
        if 'email' not in columns:
            logger.info("email字段不存在，直接添加alipay字段")
            cursor.execute("ALTER TABLE users ADD COLUMN alipay VARCHAR(20)")
        else:
            logger.info("开始迁移email字段到alipay字段")
            
            # 步骤1: 添加新的alipay字段
            cursor.execute("ALTER TABLE users ADD COLUMN alipay VARCHAR(20)")
            
            # 步骤2: 如果email字段包含手机号格式的数据，可以迁移过去
            # 这里我们不自动迁移，因为email和手机号格式不同
            # 用户需要重新填写支付宝手机号
            
            # 步骤3: 创建新表结构（不包含email字段）
            cursor.execute("""
                CREATE TABLE users_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    alipay VARCHAR(20),
                    status VARCHAR(20) DEFAULT 'active',
                    referrer_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (referrer_id) REFERENCES users(id)
                )
            """)
            
            # 步骤4: 复制数据到新表（排除email字段）
            cursor.execute("""
                INSERT INTO users_new (id, username, password_hash, alipay, status, referrer_id, created_at, updated_at)
                SELECT id, username, password_hash, NULL, status, referrer_id, created_at, updated_at
                FROM users
            """)
            
            # 步骤5: 删除旧表
            cursor.execute("DROP TABLE users")
            
            # 步骤6: 重命名新表
            cursor.execute("ALTER TABLE users_new RENAME TO users")
            
            logger.info("email字段已成功迁移为alipay字段")
        
        # 提交更改
        conn.commit()
        logger.info("数据库迁移完成")
        
    except Exception as e:
        logger.error(f"数据库迁移失败: {e}")
        if conn:
            conn.rollback()
        raise
    finally:
        if conn:
            conn.close()


def rollback_alipay_to_email(db_path: str = "ecommerce_crawler.db"):
    """
    回滚操作：将alipay字段改回email字段（仅用于开发测试）
    
    Args:
        db_path: 数据库文件路径
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        logger.info("开始回滚alipay字段到email字段")
        
        # 创建包含email字段的新表
        cursor.execute("""
            CREATE TABLE users_rollback (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100),
                password_hash VARCHAR(255) NOT NULL,
                status VARCHAR(20) DEFAULT 'active',
                referrer_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (referrer_id) REFERENCES users(id)
            )
        """)
        
        # 复制数据（alipay字段数据丢失）
        cursor.execute("""
            INSERT INTO users_rollback (id, username, email, password_hash, status, referrer_id, created_at, updated_at)
            SELECT id, username, NULL, password_hash, status, referrer_id, created_at, updated_at
            FROM users
        """)
        
        # 替换表
        cursor.execute("DROP TABLE users")
        cursor.execute("ALTER TABLE users_rollback RENAME TO users")
        
        conn.commit()
        logger.info("回滚操作完成")
        
    except Exception as e:
        logger.error(f"回滚操作失败: {e}")
        if conn:
            conn.rollback()
        raise
    finally:
        if conn:
            conn.close()


if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 执行迁移
    migrate_email_to_alipay()
