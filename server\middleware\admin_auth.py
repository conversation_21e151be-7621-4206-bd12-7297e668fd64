"""
管理员认证中间件
提供管理员JWT token验证和权限控制功能
"""
from typing import Optional
from fastapi import HTTPException, status, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from database import get_db_session
from utils.auth import get_current_admin_from_token
from models import Admin, AdminRole

# HTTP Bearer认证方案
security = HTTPBearer(auto_error=False)


class AdminAuthMiddleware:
    """管理员认证中间件类"""
    
    def __init__(self, require_auth: bool = True, required_role: Optional[AdminRole] = None):
        """
        初始化管理员认证中间件
        
        Args:
            require_auth: 是否强制要求认证，False时允许匿名访问
            required_role: 要求的最低管理员角色，None表示任何管理员角色都可以
        """
        self.require_auth = require_auth
        self.required_role = required_role
    
    def __call__(self, 
                 credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
                 db: Session = Depends(get_db_session)) -> Optional[Admin]:
        """
        中间件调用方法
        
        Args:
            credentials: HTTP Bearer认证凭据
            db: 数据库会话
            
        Returns:
            Optional[Admin]: 当前管理员对象，未认证时根据require_auth决定返回None或抛出异常
            
        Raises:
            HTTPException: 认证失败且require_auth=True时抛出401错误
        """
        if not credentials:
            if self.require_auth:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="需要管理员认证",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            return None
        
        try:
            # 获取当前管理员
            admin = get_current_admin_from_token(credentials, db)
            
            # 检查角色权限
            if self.required_role and not self._check_role_permission(admin, self.required_role):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"需要{self.required_role.value}权限"
                )
            
            return admin
            
        except HTTPException:
            if self.require_auth:
                raise
            return None
        except Exception:
            if self.require_auth:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="管理员认证失败",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            return None
    
    def _check_role_permission(self, admin: Admin, required_role: AdminRole) -> bool:
        """
        检查管理员角色权限
        
        Args:
            admin: 管理员对象
            required_role: 要求的角色
            
        Returns:
            bool: 是否有权限
        """
        # 决策理由：super_admin拥有所有权限，admin只有基础权限
        if admin.role == AdminRole.super_admin:
            return True
        
        if required_role == AdminRole.admin:
            return admin.role in [AdminRole.admin, AdminRole.super_admin]
        
        return admin.role == required_role


# 预定义的中间件实例
require_admin_auth = AdminAuthMiddleware(require_auth=True)  # 强制要求管理员认证
require_super_admin = AdminAuthMiddleware(require_auth=True, required_role=AdminRole.super_admin)  # 要求超级管理员
optional_admin_auth = AdminAuthMiddleware(require_auth=False)  # 可选管理员认证


def get_current_admin(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db_session)
) -> Admin:
    """
    获取当前认证管理员（强制要求认证）
    
    Args:
        credentials: HTTP Bearer认证凭据
        db: 数据库会话
        
    Returns:
        Admin: 当前管理员对象
        
    Raises:
        HTTPException: 认证失败时抛出401错误
    """
    return get_current_admin_from_token(credentials, db)


def get_current_super_admin(
    admin: Admin = Depends(get_current_admin)
) -> Admin:
    """
    获取当前超级管理员（要求超级管理员权限）
    
    Args:
        admin: 当前管理员
        
    Returns:
        Admin: 当前管理员对象
        
    Raises:
        HTTPException: 非超级管理员时抛出403错误
    """
    if admin.role != AdminRole.super_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要超级管理员权限"
        )
    return admin


def admin_required(
    admin: Admin = Depends(get_current_admin)
) -> Admin:
    """
    要求管理员权限的依赖（任何管理员角色都可以）
    
    Args:
        admin: 当前管理员
        
    Returns:
        Admin: 当前管理员对象
    """
    return admin


def super_admin_required(
    admin: Admin = Depends(get_current_super_admin)
) -> Admin:
    """
    要求超级管理员权限的依赖
    
    Args:
        admin: 当前管理员
        
    Returns:
        Admin: 当前管理员对象
        
    Raises:
        HTTPException: 非超级管理员时抛出403错误
    """
    return admin
